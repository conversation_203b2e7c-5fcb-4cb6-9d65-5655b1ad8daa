<!--当前使用的-->
<html>
  <head>
    <link rel="stylesheet" href="./js/lib/layui/css/layui.css" />
    <link rel="stylesheet" href="./css/pdSvg.css" />
    <script src="./js/lib/layui/layui.all.js"></script>
    <script src="./js/drawJs/jquery-2.2.4.min.js"></script>
    <script src="./js/drawJs/pdMain.js"></script>
    <script src="./js/lib/jquery.contextmenu.r2.js"></script>
    <script src="./js/drawJs/pdSvg/pdCommonPara.js"></script>
    <script src="./js/drawJs/svg.min.js"></script>
    <script src="./js/drawJs/d3.v6.min.js"></script>
    <script src="./js/drawJs/d3-interpolate.v1.min.js"></script>
    <script type="text/javascript" src="./js/lib/vis-network.min.js"></script>

    <style type="text/css">
      /* 页面整体布局 */
      body {
        margin: 0;
        padding: 0;
        height: 100vh;
        overflow: hidden;
      }

      /* 页面容器 */
      .page-container {
        display: flex;
        flex-direction: column;
        height: 100vh;
      }

      /* 搜索容器样式 */
      .search-container {
        display: flex;
        align-items: center;
      }

      #search-input {
        height: 26px;
        border: 1px solid #c0c0c0;
        border-radius: 3px 0 0 3px; /* 左侧圆角 */
        padding: 0 8px;
        font-size: 11px;
        outline: none;
        transition: border-color 0.2s;
        width: 150px; /* 您可以根据需要调整宽度 */
      }

      #search-input:focus {
        border-color: #1e9fff; /* 聚焦时高亮边框 */
      }

      #btn-search {
        border-radius: 0;
        border-left: none;
        padding: 4px 10px;
        gap: 4px;
      }

      #btn-reset-view {
        border-radius: 0 3px 3px 0;
        border-left: none;
        padding: 4px 10px;
        gap: 4px;
      }

      /* 自定义标题栏 */
      .custom-title-bar {
        height: 40px;
        background: linear-gradient(to bottom, #fafafa 0%, #e9e9e9 100%);
        border-bottom: 1px solid #d0d0d0;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 15px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        user-select: none;
        position: relative;
        z-index: 1000;
      }

      /* 标题区域 */
      .title-section {
        display: flex;
        align-items: center;
        gap: 15px;
      }

      .title-text {
        font-size: 14px;
        font-weight: bold;
        color: #333;
        display: flex;
        align-items: center;
        gap: 5px;
      }

      /* 工具栏区域 */
      .toolbar-section {
        display: flex;
        align-items: center;
        gap: 8px;
      }

      /* 下拉标签样式 */
      .dropdown-label {
        position: relative;
        display: inline-block;
      }

      .dropdown-trigger {
        padding: 4px 10px;
        /* border: 1px solid #c0c0c0; */
        background: linear-gradient(to bottom, #ffffff 0%, #f0f0f0 100%);
        border-radius: 3px;
        cursor: pointer;
        font-size: 11px;
        color: #333;
        transition: all 0.2s;
        display: flex;
        align-items: center;
        gap: 4px;
        /* height: 26px; */
        line-height: 1;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
      }

      .dropdown-trigger:hover {
        border-color: #1e9fff;
        background: linear-gradient(to bottom, #e6f7ff 0%, #bae7ff 100%);
        color: #1e9fff;
        box-shadow: 0 1px 3px rgba(30, 159, 255, 0.3);
      }

      .dropdown-trigger.active {
        background: linear-gradient(to bottom, #1e9fff 0%, #1890ff 100%);
        border-color: #1890ff;
        color: #fff;
        box-shadow: 0 1px 3px rgba(30, 159, 255, 0.5);
      }

      /* 下拉菜单 */
      .dropdown-menu {
        position: absolute;
        top: 100%;
        left: 0;
        min-width: 220px;
        background: #fff;
        border: 1px solid #d0d0d0;
        border-radius: 4px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        z-index: 2000;
        display: none;
        margin-top: 2px;
      }

      .dropdown-menu.show {
        display: block;
      }

      /* 下拉菜单内容 */
      .dropdown-content {
        padding: 12px;
      }

      /* 下拉菜单分组 */
      .dropdown-section {
        margin-bottom: 15px;
      }

      .dropdown-section:last-of-type {
        margin-bottom: 10px;
      }

      .dropdown-section-title {
        font-size: 12px;
        font-weight: bold;
        color: #666;
        margin-bottom: 8px;
        padding-bottom: 4px;
        border-bottom: 1px solid #f0f0f0;
      }

      /* 重写layui表单样式以适应下拉菜单 */
      .dropdown-menu .layui-form-checkbox,
      .dropdown-menu .layui-form-radio {
        margin: 4px 0;
        font-size: 11px;
      }

      .dropdown-menu .layui-form-checkbox span,
      .dropdown-menu .layui-form-radio span {
        font-size: 11px;
        color: #333;
      }

      .dropdown-menu .layui-form-checkbox:hover,
      .dropdown-menu .layui-form-radio:hover {
        color: #1e9fff;
      }

      .toolbar-btn {
        padding: 4px 10px;
        border: 1px solid #c0c0c0;
        background: linear-gradient(to bottom, #ffffff 0%, #f0f0f0 100%);
        border-radius: 3px;
        cursor: pointer;
        font-size: 11px;
        color: #333;
        transition: all 0.2s;
        display: flex;
        align-items: center;
        gap: 4px;
        height: 26px;
        line-height: 1;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
      }

      .toolbar-btn:hover {
        border-color: #1e9fff;
        background: linear-gradient(to bottom, #e6f7ff 0%, #bae7ff 100%);
        color: #1e9fff;
        box-shadow: 0 1px 3px rgba(30, 159, 255, 0.3);
      }

      .toolbar-btn.active {
        background: linear-gradient(to bottom, #1e9fff 0%, #1890ff 100%);
        border-color: #1890ff;
        color: #fff;
        box-shadow: 0 1px 3px rgba(30, 159, 255, 0.5);
      }

      .toolbar-separator {
        width: 1px;
        height: 20px;
        background: #d0d0d0;
        margin: 0 4px;
      }

      /* 窗口控制按钮 */
      .window-controls {
        display: flex;
        align-items: center;
        gap: 2px;
      }

      .window-btn {
        width: 28px;
        height: 26px;
        border: 1px solid #c0c0c0;
        background: linear-gradient(to bottom, #ffffff 0%, #f0f0f0 100%);
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        color: #666;
        transition: all 0.2s;
      }

      .window-btn:hover {
        background: linear-gradient(to bottom, #e6f7ff 0%, #bae7ff 100%);
        border-color: #1e9fff;
        color: #1e9fff;
      }

      .window-btn.close:hover {
        background: linear-gradient(to bottom, #ff4d4f 0%, #ff7875 100%);
        border-color: #ff4d4f;
        color: #fff;
      }

      /* 图形显示区域 */
      #mynetwork {
        flex: 1;
        width: 100%;
        border: none;
        background-color: #fff;
        box-sizing: border-box;
      }

      /* 下拉菜单内容区的样式 */
      .dropdown-content {
        padding: 10px 15px;
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
        border: 1px solid #ccc;
        background-color: #fff;
      }

      .dropdown-content .layui-form-checkbox {
        margin-bottom: 8px;
      }

      .dropdown-content .layui-form-checkbox:last-child {
        margin-bottom: 0;
      }

      /* 右键菜单样式 */
      .custom-context-menu {
        display: none;
        position: absolute;
        background: white;
        border: 1px solid #d4d4d4;
        border-radius: 4px;
        box-shadow: 2px 2px 10px rgba(0, 0, 0, 0.2);
        padding: 5px 0;
        z-index: 1000;
        min-width: 150px;
      }

      .custom-context-menu ul {
        list-style: none;
        margin: 0;
        padding: 0;
      }

      .custom-context-menu li {
        padding: 8px 15px;
        cursor: pointer;
        font-size: 14px;
        color: #333;
      }

      .custom-context-menu li:hover {
        background-color: #f0f0f0;
      }

      .custom-context-menu li i {
        margin-right: 8px;
        color: #1e9fff;
      }

      /* 节点详情弹窗样式 */
      .node-detail-content {
        padding: 15px;
      }

      .node-detail-item {
        margin-bottom: 10px;
        display: flex;
      }

      .node-detail-label {
        width: 100px;
        font-weight: bold;
        color: #555;
      }

      .node-detail-value {
        flex: 1;
        color: #333;
        word-break: break-all;
      }

      /* 复制按钮样式 */
      .copy-btn {
        position: absolute;
        right: 15px;
        top: 15px;
        cursor: pointer;
        color: #1e9fff;
        font-size: 16px;
      }

      .copy-btn:hover {
        color: #009688;
      }

      /* 复制成功提示 */
      .copy-success {
        position: fixed;
        top: 20px;
        right: 20px;
        background: #5fb878;
        color: white;
        padding: 10px 15px;
        border-radius: 4px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
        z-index: 9999;
        display: none;
      }

      .layui-form {
        height: 0 !important;
      }

      /* --- 选择性隐藏 vis.js 导航按钮 --- */
      /* 隐藏上、下、左、右四个平移按钮 */
      .vis-navigation .vis-button.vis-up,
      .vis-navigation .vis-button.vis-down,
      .vis-navigation .vis-button.vis-left,
      .vis-navigation .vis-button.vis-right,
      .vis-navigation .vis-button.vis-zoomExtends {
        display: none !important;
      }

      /* 缩放比例显示框样式 */
      #zoom-info {
        position: absolute;
        bottom: 10px;
        left: 10px;
        padding: 6px 12px;
        background-color: rgba(255, 255, 255, 0.9);
        /* border: 1px solid #d0d0d0; */
        border-radius: 4px;
        /* box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1); */
        /* font-size: 13px; */
        color: #333;
        z-index: 10;
        display: flex;
        align-items: center;
        gap: 8px;
      }

      /* 自定义图标图片样式 */
      .custom-icon {
        width: 30px;
        height: 30px;
        vertical-align: middle;
        cursor: pointer;
        transition: transform 0.3s ease;
      }

      /* 鼠标悬停时轻微放大，增加交互感 */
      .custom-icon:hover {
        transform: scale(1.1);
      }

      /* 搜索输入框获得焦点时的样式 */
      #search-input:focus {
        border-color: #1e9fff;
        box-shadow: 0 0 5px rgba(30, 159, 255, 0.3);
      }

      /* 搜索按钮激活状态 */
      #btn-search.searching {
        background: linear-gradient(to bottom, #1e9fff 0%, #1890ff 100%);
        border-color: #1890ff;
        color: #fff;
      }

      /* 搜索结果动画效果 */
      @keyframes searchPulse {
        0% {
          transform: scale(1);
          box-shadow: 0 0 0 0 rgba(255, 69, 0, 0.9);
        }
        50% {
          transform: scale(1.2);
          box-shadow: 0 0 0 20px rgba(255, 69, 0, 0.4);
        }
        100% {
          transform: scale(1);
          box-shadow: 0 0 0 40px rgba(255, 69, 0, 0);
        }
      }

      /* 增强闪烁动画 */
      @keyframes searchBlink {
        0%,
        50%,
        100% {
          opacity: 1;
        }
        25%,
        75% {
          opacity: 0.4;
        }
      }

      /* 增强光晕效果 */
      @keyframes searchGlow {
        0% {
          filter: drop-shadow(0 0 10px rgba(255, 69, 0, 1))
            drop-shadow(0 0 20px rgba(255, 69, 0, 0.8));
        }
        50% {
          filter: drop-shadow(0 0 30px rgba(255, 69, 0, 1))
            drop-shadow(0 0 50px rgba(255, 69, 0, 0.9));
        }
        100% {
          filter: drop-shadow(0 0 10px rgba(255, 69, 0, 1))
            drop-shadow(0 0 20px rgba(255, 69, 0, 0.8));
        }
      }

      /* 搜索结果节点样式 */
      .search-result-node {
        animation: searchPulse 1.5s infinite, searchGlow 2s infinite;
      }
      .search-result-blink {
        animation: searchBlink 0.5s 3;
      }
    </style>
  </head>
  <body>
    <div class="page-container">
      <!-- 自定义标题栏 -->
      <div class="custom-title-bar">
        <!-- 左侧：标题和工具栏 -->
        <div class="title-section">
          <div class="title-text">二叉图</div>

          <div class="toolbar-section">
            <!-- 下拉标签 -->
            <div class="dropdown-label">
              <div class="dropdown-trigger" id="dropdown-trigger">
                <i class="layui-icon layui-icon-more-vertical"></i>
                标签
                <i class="layui-icon layui-icon-down"></i>
              </div>
              <div class="dropdown-menu" id="dropdown-menu">
                <div class="layui-form" lay-filter="dropdownForm">
                  <div class="dropdown-content">
                    <!-- 显示选项 -->
                    <div class="dropdown-section">
                      <div class="dropdown-section-title">显示选项</div>
                      <input
                        type="checkbox"
                        name="showLabels"
                        title="显示节点名称"
                        lay-filter="dropdownForm"
                        lay-skin="primary"
                        checked
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div class="toolbar-separator"></div>

            <!-- 布局控制 -->
            <button
              class="toolbar-btn active"
              id="btn-layout-lr"
              title="左右布局"
            >
              <i class="layui-icon layui-icon-next"></i>横向
            </button>
            <button class="toolbar-btn" id="btn-layout-tb" title="上下布局">
              <i class="layui-icon layui-icon-down"></i>纵向
            </button>
            <button class="toolbar-btn" id="btn-layout-rl" title="右左布局">
              <i class="layui-icon layui-icon-prev"></i>反向
            </button>

            <div class="toolbar-separator"></div>

            <!-- 渲染引擎切换 -->
            <button class="toolbar-btn" id="btn-engine-vis" title="Vis.js引擎">
              <i class="layui-icon layui-icon-website"></i>Vis.js
            </button>
            <button
              class="toolbar-btn active"
              id="btn-engine-d3"
              title="D3.js思维导图引擎"
            >
              <i class="layui-icon layui-icon-tree"></i>D3思维导图
            </button>

            <div class="toolbar-separator"></div>

            <!-- 节点操作 -->
            <div class="search-container">
              <input
                type="text"
                id="search-input"
                placeholder="输入节点名称搜索..."
              />
              <button class="toolbar-btn" id="btn-search" title="搜索节点">
                <i class="layui-icon layui-icon-search"></i> 搜索
              </button>
              <button class="toolbar-btn" id="btn-reset-view" title="重置视图">
                <i class="layui-icon layui-icon-refresh-3"></i> 重置
              </button>
            </div>

            <!--                <div class="toolbar-separator"></div>-->

            <!-- 工具功能 -->
            <!--                <button class="toolbar-btn" id="btn-export" title="导出图片">
                                        <i class="layui-icon layui-icon-download-circle"></i>导出
                                    </button>-->
            <button class="toolbar-btn" id="btn-fullscreen" title="全屏显示">
              <i class="layui-icon layui-icon-screen-full"></i>全屏
            </button>
          </div>
        </div>

        <!-- 右侧：窗口控制按钮 -->
        <div class="window-controls">
          <!--            <button class="window-btn" id="btn-minimize" title="最小化">
                              <i class="layui-icon layui-icon-subtraction"></i>
                          </button>
                          <button class="window-btn" id="btn-maximize" title="最大化">
                              <i class="layui-icon layui-icon-addition"></i>
                          </button>-->
          <button class="window-btn close" id="btn-close" title="关闭">
            <i class="layui-icon layui-icon-close"></i>
          </button>
        </div>
      </div>

      <!-- 图形显示区域 -->
      <div id="mynetwork"></div>
      <!-- D3.js SVG容器 -->
      <svg id="d3-mindmap" style="display: none"></svg>

      <!-- 缩放比例显示框 -->
      <div id="zoom-info">
        <img
          id="reset-zoom-icon"
          src="./image/restore100_gwl_circle.png"
          class="custom-icon"
          title="重置视图到100%"
        />
        <span>缩放比例: <span id="zoom-level">100%</span></span>
      </div>
    </div>
    <!--右键功能-->
    <!--<div id="customContextMenu" class="custom-context-menu">
<ul>
    <li onclick="showNodeDetails()">
        <i class="layui-icon">&#xe60a;</i>查看详情
    </li>
    &lt;!&ndash;        <li onclick="expandNode()"><i class="layui-icon">&#xe623;</i>展开节点</li>
            <li onclick="collapseNode()"><i class="layui-icon">&#xe625;</i>折叠节点</li>&ndash;&gt;
    <li onclick="copyNodeInfo()">
        <i class="layui-icon">&#xe64e;</i>复制信息
    </li>
</ul>
</div>-->

    <div id="copySuccess" class="copy-success">
      <i class="layui-icon">&#xe616;</i> 复制成功！
    </div>

    <script type="text/javascript">
      // 全局变量
      var network = null;
      var selectedNodeId = null;
      var selectedNodeData = null;

      var initialPositions = {};
      var initialViewScale = 1.0; // 用于存储初始缩放比例
      var initialViewPosition = { x: 0, y: 0 }; // 用于存储初始中心位置

      // 全局更新缩放比例函数
      function updateZoomLevel(scale) {
        if (!scale) {
          // 如果事件参数没有提供 scale，手动获取
          if (network) {
            scale = network.getScale();
          } else if (d3Zoom) {
            // 对于D3，尝试获取当前缩放比例
            scale = d3.zoomTransform(d3Svg.node()).k;
          } else {
            return;
          }
        }

        // 计算百分比，保留两位小数
        var percentage = (scale * 100).toFixed(0);

        // 限制最小显示为 1% (防止比例过小显示负数或科学计数法)
        if (percentage < 1) {
          percentage = 1;
        }

        $("#zoom-level").text(percentage + "%");
      }

      //初始化加载数据
      layui.use(["jquery", "layer", "form"], function () {
        var $ = layui.$;
        var layer = layui.layer;
        var form = layui.form;

        var tipsIndex = null;
        var closeTimer = null;

        function closeTips() {
          if (tipsIndex) {
            layer.close(tipsIndex);
            tipsIndex = null;
          }
        }

        // --- 鼠标进入触发元素 ---
        $("#tool-settings-btn").on("mouseenter", function () {
          // 如果鼠标再次进入，清除可能存在的关闭定时器
          if (closeTimer) {
            clearTimeout(closeTimer);
            closeTimer = null;
          }

          // 如果 tips 已经存在，则不再重复创建
          if (tipsIndex) {
            return;
          }

          var dropdownHtml =
            '<div class="layui-form" lay-filter="binaryTreeForm">' +
            '<div class="dropdown-content">' +
            '<input type="checkbox" name="option1" title="标签选项一" lay-skin="primary" checked>' +
            '<input type="checkbox" name="option2" title="标签选项二" lay-skin="primary">' +
            '<input type="checkbox" name="option3" title="标签选项三" lay-skin="primary">' +
            "</div>" +
            "</div>";

          // 创建 tips 并保存索引
          tipsIndex = layer.tips(dropdownHtml, this, {
            tips: [4, "#fff"],
            time: 0,
            skin: "layui-layer-molv",
            success: function (tipsLayero, index) {
              // 渲染表单
              form.render("checkbox", "binaryTreeForm");

              tipsLayero.on("mouseenter", function () {
                if (closeTimer) {
                  clearTimeout(closeTimer);
                  closeTimer = null;
                }
              });

              // 鼠标离开 tips 层，启动关闭定时器
              tipsLayero.on("mouseleave", function () {
                closeTimer = setTimeout(function () {
                  closeTips();
                }, 100); // 100毫秒延迟
              });

              // 监听多选框事件
              form.on("checkbox(binaryTreeForm)", function (data) {
                console.log(
                  "多选框状态改变:",
                  data.elem.title,
                  "是否选中:",
                  data.elem.checked
                );
              });
            },
          });
        });

        $("#tool-settings-btn").on("mouseleave", function () {
          // 启动一个关闭定时器
          closeTimer = setTimeout(function () {
            closeTips();
          }, 100);
        });

        // 解析URL参数函数
        function getQueryParams() {
          const params = {};
          const queryString = window.location.search.substring(1);
          const pairs = queryString.split("&");

          for (let i = 0; i < pairs.length; i++) {
            const pair = pairs[i].split("=");
            const key = decodeURIComponent(pair[0]);
            const value = decodeURIComponent(pair[1] || "");
            params[key] = value;
          }

          return params;
        }

        const params = getQueryParams();

        //  从URL参数中获取标题
        var pageTitle = params.title;

        if (pageTitle) {
          // 直接修改它的文本内容
          $(".title-text").text(pageTitle + "的台区");
        }

        // 获取容器
        var container = document.getElementById("mynetwork");
        $.ajax({
          url: commonUrl.baseUrl + "userTransAnalysis/dataAnalysis0822",
          contentType: "application/x-www-form-urlencoded",
          type: "get",
          dataType: "json",
          xhrFields: {
            withCredentials: true,
          },
          headers: {
            Authorization: "Dwqsk " + tokens,
          },
          data: {
            psrId: params.psrId,
          },
          success: function (data) {
            let nodes = [];
            let yijiaruLine = [];
            let dataList = data.dvList;
            let dvList = data.pocessedVLList;

            const transformerIds = new Set();

            for (let i = 0; i < dataList.length; i++) {
              let nodeData = dataList[i];
              let data = dataList[i];
              data.label = data.psrTypeName;
              data.data_id = data.id;

              data.shape = "image";
              let id = data.geoPsrId;

              data.id = id;
              if (data.psrTypeName == undefined || data.psrTypeName == "") {
                if (data.psrType == "3112") {
                  data.psrTypeName = "计量箱";
                }
                if (data.psrType == "3218000") {
                  data.psrTypeName = "低压用户接入点";
                }
              }
              //节点图形
              data.image = "./image/jx.png";
              if (data.psrTypeName == "计量箱") {
                data.image = "./image/jx.png";
              }
              if (data.psrTypeName == "墙支架") {
                data.image = "./image/T.png";
              }
              if (data.psrTypeName == "低压用户接入点") {
                data.image = "./image/j.png";
              }
              if (data.psrTypeName == "柱上变压器(公用变)") {
                data.image = "./image/b.png";
              }
              if (data.psrTypeName == "断路器") {
                data.image = "./image/dlq.png";
              }

              // 判断当前节点是“柱上变压器（公用变）”
              if (nodeData.psrTypeName === "柱上变压器(公用变)") {
                transformerIds.add(nodeData.id);
              }

              // 添加标题用于悬停提示
              nodeData.title = `ID: ${nodeData.data_id}\n类型: ${nodeData.psrTypeName}\n连接: ${nodeData.connection}`;
              nodes.push(nodeData);
            }

            let edges = [];
            for (let i = 0; i < dvList.length; i++) {
              let dvlist = dvList[i];

              let fromId = dvlist.startGeoPsrId;
              let toId = dvlist.endGeoPsrId;

              // 如果一条边的终点(to)是一个变压器，就反转这条边
              if (transformerIds.has(toId)) {
                // 交换 from 和 to
                [fromId, toId] = [toId, fromId];
                console.log(
                  `边已反转: 原[${dvlist.startGeoPsrId} -> ${dvlist.endGeoPsrId}], 现[${fromId} -> ${toId}]`
                );
              }
              edges.push({ from: fromId, to: toId });
            }

            console.log("页面显示图元数量=" + nodes.length);

            var nodes1 = new vis.DataSet(nodes);
            var edges1 = new vis.DataSet(edges);
            // 将数据赋值给vis 数据格式化器
            var data = {
              nodes: nodes1,
              edges: edges1,
            };

            // 配置选项
            var options = {
              layout: {
                hierarchical: {
                  enabled: true,
                  direction: "LR", // 改为横向（Left → Right）
                  sortMethod: "hubsize", // hubsize 一般能减少交叉
                  shakeTowards: "roots", // 尽量往根对齐，减少交错
                  nodeSpacing: 220, // 同层节点间距（可以调大/调小）
                  levelSeparation: 280, // 层与层之间的距离
                },
              },
              nodes: {
                size: 30,
                font: {
                  size: 14,
                  face: "Tahoma",
                },
                borderWidth: 2,
                color: {
                  border: "#2B7CE9", // 默认边框颜色
                  background: "#FFFFFF", // 默认背景色
                  highlight: {
                    // 高亮状态下的颜色
                    border: "#FF4500", // 橙红色边框，非常醒目
                    background: "#FFE4B5", // 较浅的橙色背景
                  },
                },
                borderWidthSelected: 4, // 选中时边框宽度加粗
                shadow: {
                  // 为节点添加阴影效果
                  enabled: true,
                  color: "rgba(0,0,0,0.5)",
                  size: 10,
                  x: 5,
                  y: 5,
                },
              },
              edges: {
                width: 2,
                color: "#848484",
                smooth: {
                  enabled: true,
                  type: "cubicBezier", // 使用贝塞尔曲线来自定义路径
                  forceDirection: "horizontal",
                  roundness: 0.1,
                },
                arrows: {
                  to: {
                    enabled: false, //箭头方向
                    scaleFactor: 1,
                  },
                },
              },
              physics: {
                enabled: false,
              },
              interaction: {
                hover: true,
                tooltipDelay: 200,
                navigationButtons: true, //导航
                keyboard: true,
                dragNodes: false, //节点是否拖动
                selectConnectedEdges: false, //边不需要高亮
              },
            };

            // 保存数据供D3.js使用
            d3MindmapData = {
              nodes: nodes,
              edges: edges,
            };

            // 根据当前引擎初始化相应的图形
            if (currentEngine === "d3") {
              // 隐藏vis.js容器，显示D3容器
              container.style.display = "none";
              initD3Mindmap(nodes, edges);
            } else {
              // 初始化vis.js关系图
              network = new vis.Network(container, data, options);

              // --- 监听视图变化并更新缩放比例 ---
              network.on("zoom", function (params) {
                updateZoomLevel(params.scale);
              });

              // 首帧绘制完成后，记录初始状态并初始化
              network.once("afterDrawing", function () {
                // 记录节点的初始物理位置
                initialPositions = network.getPositions(nodes1.getIds());

                initialViewScale = network.getScale();
                initialViewPosition = network.getViewPosition();

                //更新左下角的UI，显示初始的、真实的缩放比例
                updateZoomLevel(initialViewScale);
              });
            }

            // 初始化工具栏事件（只需要初始化一次）
            initToolbarEvents();

            function snapshotPositions() {
              initialPositions = network.getPositions(nodes1.getIds());
            }

            // 初始化下拉菜单
            function initDropdownMenu() {
              var $trigger = $("#dropdown-trigger");
              var $menu = $("#dropdown-menu");
              var isMenuOpen = false;

              // 点击触发器
              $trigger.click(function (e) {
                e.stopPropagation();
                if (isMenuOpen) {
                  closeDropdownMenu();
                } else {
                  openDropdownMenu();
                }
              });

              // 鼠标悬停触发器
              $trigger.mouseenter(function () {
                if (!isMenuOpen) {
                  openDropdownMenu();
                }
              });

              // 鼠标离开下拉区域
              $(".dropdown-label").mouseleave(function () {
                setTimeout(function () {
                  if (!$(".dropdown-label:hover").length) {
                    closeDropdownMenu();
                  }
                }, 100);
              });

              // 阻止菜单内部点击关闭菜单
              $menu.click(function (e) {
                e.stopPropagation();
              });

              // 点击页面其他地方关闭菜单
              $(document).click(function () {
                closeDropdownMenu();
              });

              // 监听表单变化，立即生效
              form.on("checkbox(dropdownForm)", function (data) {
                handleCheckboxChange(data);
              });

              function openDropdownMenu() {
                $menu.addClass("show");
                $trigger.addClass("active");
                isMenuOpen = true;
                // 重新渲染layui表单
                form.render();
              }

              function closeDropdownMenu() {
                $menu.removeClass("show");
                $trigger.removeClass("active");
                isMenuOpen = false;
              }
            }

            // 处理复选框变化
            function handleCheckboxChange(data) {
              // 确保 network 对象已初始化
              if (!network) return;
              var checkboxName = data.elem.name;
              var isChecked = data.elem.checked;

              switch (checkboxName) {
                case "showLabels":
                  // 更新 vis.js 网络图的选项
                  network.setOptions({
                    nodes: {
                      font: {
                        // 如果选中，则设置字体大小为 14；否则设置为 0 来隐藏标签
                        size: isChecked ? 14 : 0,
                      },
                    },
                  });
                  layer.msg(isChecked ? "已显示节点名称" : "已隐藏节点名称", {
                    time: 1500,
                  });
                  break;
              }
            }

            // 初始化工具栏事件
            function initToolbarEvents() {
              // 初始化下拉菜单
              initDropdownMenu();
              // 布局切换 - 横向
              $("#btn-layout-lr").click(function () {
                if (!$(this).hasClass("active")) {
                  switchLayout("LR");
                  $('.toolbar-btn[id^="btn-layout-"]').removeClass("active");
                  $(this).addClass("active");
                }
              });

              // 布局切换 - 纵向
              $("#btn-layout-tb").click(function () {
                if (!$(this).hasClass("active")) {
                  switchLayout("UD");
                  $('.toolbar-btn[id^="btn-layout-"]').removeClass("active");
                  $(this).addClass("active");
                }
              });

              // 布局切换 - 反向
              $("#btn-layout-rl").click(function () {
                if (!$(this).hasClass("active")) {
                  switchLayout("RL");
                  $('.toolbar-btn[id^="btn-layout-"]').removeClass("active");
                  $(this).addClass("active");
                }
              });

              // 引擎切换 - Vis.js
              $("#btn-engine-vis").click(function () {
                if (!$(this).hasClass("active")) {
                  switchEngine("vis");
                  $('.toolbar-btn[id^="btn-engine-"]').removeClass("active");
                  $(this).addClass("active");
                }
              });

              // 引擎切换 - D3.js思维导图
              $("#btn-engine-d3").click(function () {
                if (!$(this).hasClass("active")) {
                  switchEngine("d3");
                  $('.toolbar-btn[id^="btn-engine-"]').removeClass("active");
                  $(this).addClass("active");
                }
              });

              // 搜索节点
              $("#btn-search").click(function () {
                performSearch();
              });

              // 搜索输入框回车事件
              $("#search-input").keypress(function (e) {
                if (e.which === 13) {
                  // 回车键
                  performSearch();
                }
              });

              // 重置视图按钮
              $("#btn-reset-view").click(function () {
                resetSearchView();
              });

              // 为重置缩放比例的图标绑定点击事件 (恢复到初始“全局视图”)
              $("#reset-zoom-icon").on("click", function () {
                if (currentEngine === "vis" && network) {
                  // Vis.js重置视图
                  network.moveTo({
                    position: initialViewPosition, // 使用保存的初始位置
                    scale: initialViewScale, // 使用保存的初始缩放比例
                    animation: {
                      duration: 600,
                      easingFunction: "easeInOutQuad",
                    },
                  });
                  updateZoomLevel(initialViewScale);
                } else if (currentEngine === "d3") {
                  // D3.js重置视图
                  fitD3View();
                }
              });

              // 导出图片
              $("#btn-export").click(function () {
                exportNetworkImage();
              });

              // 全屏显示
              $("#btn-fullscreen").click(function () {
                toggleFullscreen();
              });

              // 窗口控制按钮
              $("#btn-minimize").click(function () {
                // 检查是否在 iframe 中，并且父页面有 layer 对象
                if (window.parent && window.parent.layer) {
                  try {
                    var index = parent.layer.getFrameIndex(window.name);
                    parent.layer.min(index);
                  } catch (e) {
                    console.error("最小化失败:", e);
                    layer.msg("最小化功能不可用");
                  }
                } else {
                  layer.msg("最小化功能需要在弹窗中使用");
                }
              });

              $("#btn-maximize").click(function () {
                if (window.parent && window.parent.layer) {
                  try {
                    var index = parent.layer.getFrameIndex(window.name);
                    parent.layer.full(index);
                  } catch (e) {
                    console.error("最大化失败:", e);
                    layer.msg("最大化功能不可用");
                  }
                } else {
                  layer.msg("最大化功能需要在弹窗中使用");
                }
              });

              $("#btn-close").click(function () {
                if (window.parent && window.parent.layer) {
                  try {
                    var index = parent.layer.getFrameIndex(window.name);
                    // 调用父页面的 layer.close 方法
                    parent.layer.close(index);
                  } catch (e) {
                    console.error("关闭失败:", e);
                    layer.msg("关闭功能不可用");
                  }
                } else {
                  if (confirm("确定要关闭页面吗？")) {
                    window.close();
                  }
                }
              });
            }

            // 只在vis.js模式下添加事件监听器
            if (currentEngine === "vis" && network) {
              //拖动结束时，把被拖动的节点移回原位
              network.on("dragEnd", function (params) {
                if (!params.nodes || params.nodes.length === 0) return;
                params.nodes.forEach(function (id) {
                  var pos = initialPositions[id];
                  if (pos) network.moveNode(id, pos.x, pos.y);
                });
                network.fit({
                  animation: { duration: 300, easingFunction: "easeInOutQuad" },
                });
              });

              // 添加右键菜单事件
              network.on("oncontext", function (params) {
                console.log("--- 鼠标右键事件 (oncontext) 触发 ---");
                console.log("事件参数 (params):", params);
                params.event.preventDefault();

                if (params.nodes && params.nodes.length > 0) {
                  selectedNodeId = params.nodes[0];
                  selectedNodeData = nodes1.get(selectedNodeId);

                  console.log("右键点击了节点 ID:", selectedNodeId);

                  var menu = document.getElementById("customContextMenu");
                  menu.style.display = "block";
                  menu.style.left = params.pointer.DOM.x + "px";
                  menu.style.top = params.pointer.DOM.y + "px";
                } else {
                  selectedNodeId = null;
                  selectedNodeData = null;
                  console.log("右键点击了空白区域。");
                  document.getElementById("customContextMenu").style.display =
                    "none";
                }
              });

              // 2. 添加单击事件 (click)
              network.on("click", function (params) {
                console.log("--- 鼠标单击事件 (click) 触发 ---");
                console.log("事件参数 (params):", params);
                document.getElementById("customContextMenu").style.display =
                  "none";
                if (params.nodes && params.nodes.length > 0) {
                  var clickedNodeId = params.nodes[0];
                  var clickedNodeData = nodes1.get(clickedNodeId);

                  console.log("单击了节点 ID:", clickedNodeId);
                  console.log("节点数据:", clickedNodeData);
                } else {
                  console.log("单击了空白区域。");
                }
              });

              // 3. 添加双击事件 (doubleClick)
              network.on("doubleClick", function (params) {
                console.log("--- 鼠标双击事件 (doubleClick) 触发 ---");
                console.log("事件参数 (params):", params);

                if (params.nodes && params.nodes.length > 0) {
                  selectedNodeId = params.nodes[0];
                  selectedNodeData = nodes1.get(selectedNodeId);

                  console.log(
                    "双击了节点 ID:",
                    selectedNodeId,
                    "，准备显示详情。"
                  );
                  showNodeDetails();
                } else {
                  console.log("双击了空白区域。");
                }
              });
            }
          },
        });
      });

      // 显示节点详情
      function showNodeDetails() {
        if (!selectedNodeData) return;

        // 隐藏右键菜单
        document.getElementById("customContextMenu").style.display = "none";

        // 使用layui的弹窗
        layer.open({
          type: 1,
          title: "节点详情 - " + selectedNodeData.label,
          area: ["500px", "400px"],
          content: createNodeDetailContent(selectedNodeData),
          btn: ["关闭"],
          yes: function (index, layero) {
            layer.close(index);
          },
        });
      }

      // 创建节点详情内容
      function createNodeDetailContent(nodeData) {
        let content = `
                <div class="node-detail-content">
                    <div class="node-detail-item">
                        <span class="node-detail-label">节点ID:</span>
                        <span class="node-detail-value">${
                          nodeData.id || ""
                        }</span>
                    </div>
                    <div class="node-detail-item">
                        <span class="node-detail-label">数据ID:</span>
                        <span class="node-detail-value">${
                          nodeData.data_id || ""
                        }</span>
                    </div>
                    <div class="node-detail-item">
                        <span class="node-detail-label">类型名称:</span>
                        <span class="node-detail-value">${
                          nodeData.psrTypeName || ""
                        }</span>
                    </div>
                    <div class="node-detail-item">
                        <span class="node-detail-label">连接信息:</span>
                        <span class="node-detail-value">${
                          nodeData.connection || ""
                        }</span>
                    </div>
            `;

        // 添加其他可能存在的属性
        for (let key in nodeData) {
          if (
            ![
              "id",
              "data_id",
              "psrTypeName",
              "connection",
              "label",
              "title",
            ].includes(key)
          ) {
            content += `
                        <div class="node-detail-item">
                            <span class="node-detail-label" style="word-wrap: break-word">${key}:</span>
                            <span class="node-detail-value">${
                              nodeData[key] || ""
                            }</span>
                        </div>
                    `;
          }
        }

        content += `</div>`;
        return content;
      }

      // 复制节点信息
      function copyNodeInfo() {
        if (!selectedNodeData) return;

        // 隐藏右键菜单
        document.getElementById("customContextMenu").style.display = "none";

        // 构建要复制的文本内容
        let textToCopy = `节点详情信息\n`;
        textToCopy += `============\n`;
        textToCopy += `节点ID: ${selectedNodeData.id || "无"}\n`;
        textToCopy += `数据ID: ${selectedNodeData.data_id || "无"}\n`;
        textToCopy += `类型名称: ${selectedNodeData.psrTypeName || "无"}\n`;
        textToCopy += `连接信息: ${selectedNodeData.connection || "无"}\n`;

        // 添加其他属性
        for (let key in selectedNodeData) {
          if (
            ![
              "id",
              "data_id",
              "psrTypeName",
              "connection",
              "label",
              "title",
            ].includes(key)
          ) {
            textToCopy += `${key}: ${selectedNodeData[key] || "无"}\n`;
          }
        }

        // 使用现代 Clipboard API 复制文本
        navigator.clipboard
          .writeText(textToCopy)
          .then(function () {
            // 显示复制成功提示
            showCopySuccess();
          })
          .catch(function (err) {
            // 如果 Clipboard API 不可用，使用备用方法
            copyTextFallback(textToCopy);
          });
      }

      // 显示复制成功提示
      function showCopySuccess() {
        var successMsg = document.getElementById("copySuccess");
        successMsg.style.display = "block";

        // 2秒后自动隐藏
        setTimeout(function () {
          successMsg.style.display = "none";
        }, 2000);
      }

      // 复制文本的备用方法
      function copyTextFallback(text) {
        // 创建临时textarea元素
        var textarea = document.createElement("textarea");
        textarea.value = text;
        textarea.style.position = "fixed";
        textarea.style.opacity = "0";

        document.body.appendChild(textarea);
        textarea.select();

        try {
          var successful = document.execCommand("copy");
          if (successful) {
            showCopySuccess();
          } else {
            layer.msg("复制失败，请手动复制");
          }
        } catch (err) {
          layer.msg("复制失败: " + err);
        }

        document.body.removeChild(textarea);
      }

      // 点击页面其他地方隐藏菜单 右键
      /*document.addEventListener("click", function (e) {
          var menu = document.getElementById("customContextMenu");
          if (menu.style.display === "block" && !menu.contains(e.target)) {
              menu.style.display = "none";
          }
      });*/

      // ========== 工具栏功能函数 ==========

      // 切换布局
      function switchLayout(direction) {
        if (!network) return;

        var currentOptions = network.getOptionsFromConfigurator();
        var newOptions = {
          layout: {
            hierarchical: {
              enabled: true,
              direction: direction, // 'LR', 'RL', 'UD', 'DU'
              sortMethod: "hubsize",
              shakeTowards: "roots",
              nodeSpacing: 220,
              levelSeparation: 280,
            },
          },
        };

        network.setOptions(newOptions);

        // 重新布局后适应窗口
        setTimeout(function () {
          network.fit({
            animation: { duration: 500, easingFunction: "easeInOutQuad" },
          });
        }, 100);
      }

      // 切换渲染引擎
      function switchEngine(engine) {
        currentEngine = engine;

        if (engine === "vis") {
          // 切换到Vis.js
          document.getElementById("mynetwork").style.display = "block";
          document.getElementById("d3-mindmap").style.display = "none";

          if (network) {
            network.redraw();
            network.fit();
          }
          layer.msg("已切换到Vis.js引擎", { time: 1500 });
        } else if (engine === "d3") {
          // 切换到D3.js
          document.getElementById("mynetwork").style.display = "none";
          document.getElementById("d3-mindmap").style.display = "block";

          // 如果有数据，重新初始化D3思维导图
          if (d3MindmapData) {
            initD3Mindmap(d3MindmapData.nodes, d3MindmapData.edges);
          }
          layer.msg("已切换到D3.js思维导图引擎", { time: 1500 });
        }
      }

      // 处理节点点击事件
      function handleNodeClick(nodeData) {
        selectedNodeData = nodeData;
        console.log("点击了节点:", nodeData);
      }

      // 处理节点双击事件
      function handleNodeDoubleClick(nodeData) {
        selectedNodeData = nodeData;
        showNodeDetails();
      }

      // 启用思维导图连线绘制
      function enableMindmapEdges() {
        if (!network) return;

        // 监听绘制事件，自定义连线绘制
        network.on("afterDrawing", function (ctx) {
          drawMindmapEdges(ctx);
        });
      }

      // 禁用思维导图连线绘制
      function disableMindmapEdges() {
        if (!network) return;
        // 移除自定义绘制监听器
        network.off("afterDrawing");
      }

      // 绘制思维导图样式的连线
      function drawMindmapEdges(ctx) {
        var edges = network.body.data.edges;
        var nodePositions = network.getPositions();

        // 保存当前绘制状态
        ctx.save();

        // 设置连线样式
        ctx.strokeStyle = "#848484";
        ctx.lineWidth = 2;
        ctx.lineCap = "round";
        ctx.lineJoin = "round";

        edges.forEach(function (edge) {
          var fromPos = nodePositions[edge.from];
          var toPos = nodePositions[edge.to];

          if (fromPos && toPos) {
            // 绘制思维导图样式连线
            drawMindmapLine(ctx, fromPos, toPos);
          }
        });

        // 恢复绘制状态
        ctx.restore();
      }

      // 绘制单条思维导图连线
      function drawMindmapLine(ctx, fromPos, toPos) {
        ctx.beginPath();

        // 计算节点边缘的连接点（避免连线穿过节点）
        var nodeRadius = 15; // 节点半径
        var fromX =
          fromPos.x + (fromPos.x < toPos.x ? nodeRadius : -nodeRadius);
        var toX = toPos.x + (toPos.x > fromPos.x ? -nodeRadius : nodeRadius);

        // 计算中间转折点
        var horizontalDistance = Math.abs(toX - fromX);
        var midX = fromX + horizontalDistance * 0.6; // 水平延伸60%的距离

        // 绘制思维导图连线路径：起点 -> 水平线 -> 垂直线 -> 终点
        ctx.moveTo(fromX, fromPos.y);
        ctx.lineTo(midX, fromPos.y); // 水平线段
        ctx.lineTo(midX, toPos.y); // 垂直线段
        ctx.lineTo(toX, toPos.y); // 连接到目标节点

        ctx.stroke();
      }

      // 导出网络图片
      function exportNetworkImage() {
        if (!network) return;

        try {
          var canvas = network.canvas.frame.canvas;
          var dataURL = canvas.toDataURL("image/png");

          // 创建下载链接
          var link = document.createElement("a");
          link.download = "二叉树汉字图_" + new Date().getTime() + ".png";
          link.href = dataURL;

          // 触发下载
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);

          layer.msg("图片导出成功");
        } catch (error) {
          console.error("导出失败:", error);
          layer.msg("导出失败，请重试");
        }
      }

      // 全屏功能
      function toggleFullscreen() {
        var elem = document.documentElement;

        if (!document.fullscreenElement) {
          // 进入全屏
          if (elem.requestFullscreen) {
            elem.requestFullscreen();
          } else if (elem.webkitRequestFullscreen) {
            elem.webkitRequestFullscreen();
          } else if (elem.msRequestFullscreen) {
            elem.msRequestFullscreen();
          }
          $("#btn-fullscreen").addClass("active");
          layer.msg("已进入全屏模式", { time: 1000 });
        } else {
          // 退出全屏
          if (document.exitFullscreen) {
            document.exitFullscreen();
          } else if (document.webkitExitFullscreen) {
            document.webkitExitFullscreen();
          } else if (document.msExitFullscreen) {
            document.msExitFullscreen();
          }
          $("#btn-fullscreen").removeClass("active");
          layer.msg("已退出全屏模式", { time: 1000 });
        }
      }

      // 监听全屏状态变化
      document.addEventListener("fullscreenchange", function () {
        if (!document.fullscreenElement) {
          $("#btn-fullscreen").removeClass("active");
        }
      });

      // ========== 搜索功能实现 ==========
      var searchResults = [];
      var currentSearchIndex = 0;
      var originalNodeColors = {};

      // 执行搜索
      function performSearch() {
        var searchTerm = $("#search-input").val().trim();

        if (!searchTerm) {
          layer.msg("请输入搜索内容", { time: 1500 });
          return;
        }

        if (!network) {
          layer.msg("网络图未初始化", { time: 1500 });
          return;
        }

        // 显示搜索状态
        $("#btn-search").addClass("searching");
        $("#btn-search")
          .find("i")
          .removeClass("layui-icon-search")
          .addClass(
            "layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop"
          );

        // 重置之前的搜索结果
        resetSearchHighlight();

        // 获取所有节点数据
        var allNodes = network.body.data.nodes.get();
        searchResults = [];

        // 搜索匹配的节点
        allNodes.forEach(function (node) {
          var searchFields = [
            node.psrTypeName || "",
            node.label || "",
            node.data_id || "",
            node.id || "",
            node.connection || "",
          ];

          // 检查是否有任何字段包含搜索词（不区分大小写）
          var isMatch = searchFields.some(function (field) {
            return (
              String(field).toLowerCase().indexOf(searchTerm.toLowerCase()) !==
              -1
            );
          });

          if (isMatch) {
            searchResults.push(node.id);
          }
        });

        // 恢复搜索按钮状态
        function restoreSearchButton() {
          $("#btn-search").removeClass("searching");
          $("#btn-search")
            .find("i")
            .removeClass(
              "layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop"
            )
            .addClass("layui-icon-search");
        }

        if (searchResults.length === 0) {
          restoreSearchButton();
          layer.msg("未找到匹配的节点", { time: 2000 });
          return;
        }

        // 高亮所有搜索结果
        highlightSearchResults();

        // 定位到第一个结果
        currentSearchIndex = 0;
        focusOnSearchResult(currentSearchIndex);

        // 显示搜索结果信息
        layer.msg(`找到 ${searchResults.length} 个匹配节点`, { time: 2000 });

        // 延迟恢复按钮状态，让用户看到加载效果
        setTimeout(restoreSearchButton, 800);
      }

      // 高亮搜索结果
      function highlightSearchResults() {
        if (!network || searchResults.length === 0) return;

        var nodes = network.body.data.nodes;
        var updateNodes = [];

        searchResults.forEach(function (nodeId) {
          var node = nodes.get(nodeId);
          if (node) {
            // 保存原始颜色
            if (!originalNodeColors[nodeId]) {
              originalNodeColors[nodeId] = {
                border: node.color ? node.color.border : "#2B7CE9",
                background: node.color ? node.color.background : "#FFFFFF",
                size: node.size || 30, // 保存原始尺寸
              };
            }

            // 设置更鲜艳的高亮颜色和更大尺寸
            updateNodes.push({
              id: nodeId,
              color: {
                border: "#FF0000",
                background: "#FFDD44",
                highlight: {
                  border: "#FF3333",
                  background: "#FFFF99",
                },
              },
              borderWidth: 8,
              shadow: {
                enabled: true,
                color: "rgba(255, 0, 0, 0.8)",
                size: 25,
                x: 0,
                y: 0,
              },
              size: node.size ? node.size * 1.5 : 45,
              font: {
                size: 18,
                face: "Tahoma",
                color: "#FF0000", // 标签使用红色
                bold: true, // 加粗标签
              },
            });
          }
        });

        if (updateNodes.length > 0) {
          nodes.update(updateNodes);

          // 添加闪烁动画效果
          setTimeout(function () {
            addBlinkAnimation();
          }, 100);

          // 添加持续的脉冲和光晕效果
          setTimeout(function () {
            addPulseAnimation();
          }, 1500);
        }
      }
      // 定位到指定的搜索结果
      function focusOnSearchResult(index) {
        if (!network || !searchResults || index >= searchResults.length) return;

        var nodeId = searchResults[index];
        var nodePosition = network.getPositions([nodeId])[nodeId];

        if (nodePosition) {
          // 移动视图到节点位置并动态放大
          network.moveTo({
            position: { x: nodePosition.x, y: nodePosition.y },
            scale: 1.8, // 放大到180%，
            animation: {
              duration: 1000, // 动画
              easingFunction: "easeInOutQuad",
            },
          });

          // 选中节点
          network.selectNodes([nodeId]);

          // 更新缩放显示
          setTimeout(function () {
            updateZoomLevel(1.8);
          }, 1000);
        }
      }

      // 添加闪烁动画效果
      function addBlinkAnimation() {
        if (!network || searchResults.length === 0) return;

        searchResults.forEach(function (nodeId) {
          // 获取DOM中的节点元素
          var nodeElement = network.body.nodes[nodeId];
          if (nodeElement && nodeElement.shape && nodeElement.shape.body) {
            var domElement = nodeElement.shape.body;
            if (domElement.classList) {
              domElement.classList.add("search-result-blink");

              // 1.5秒后移除闪烁效果
              setTimeout(function () {
                if (domElement.classList) {
                  domElement.classList.remove("search-result-blink");
                }
              }, 2500);
            }
          }
        });
      }

      // 添加脉冲和光晕动画效果
      function addPulseAnimation() {
        if (!network || searchResults.length === 0) return;

        searchResults.forEach(function (nodeId) {
          // 获取DOM中的节点元素
          var nodeElement = network.body.nodes[nodeId];
          if (nodeElement && nodeElement.shape && nodeElement.shape.body) {
            var domElement = nodeElement.shape.body;
            if (domElement.classList) {
              domElement.classList.add("search-result-node");
            }
          }
        });
      }

      // 移除所有动画效果
      function removeAllAnimations() {
        if (!network) return;

        // 移除所有节点的动画类
        Object.keys(network.body.nodes).forEach(function (nodeId) {
          var nodeElement = network.body.nodes[nodeId];
          if (nodeElement && nodeElement.shape && nodeElement.shape.body) {
            var domElement = nodeElement.shape.body;
            if (domElement.classList) {
              domElement.classList.remove(
                "search-result-node",
                "search-result-blink"
              );
            }
          }
        });
      }

      // 重置搜索高亮
      function resetSearchHighlight() {
        if (!network) return;

        // 首先移除所有动画效果
        removeAllAnimations();

        var nodes = network.body.data.nodes;
        var edges = network.body.data.edges;
        var updateNodes = [];
        var updateEdges = [];

        // 恢复所有节点的原始颜色和大小
        Object.keys(originalNodeColors).forEach(function (nodeId) {
          var originalColor = originalNodeColors[nodeId];
          var originalNode = nodes.get(nodeId);

          updateNodes.push({
            id: nodeId,
            color: {
              border: originalColor.border,
              background: originalColor.background,
              highlight: {
                border: "#FF4500",
                background: "#FFE4B5",
              },
            },
            borderWidth: 2,
            shadow: {
              enabled: true,
              color: "rgba(0,0,0,0.5)",
              size: 10,
              x: 5,
              y: 5,
            },
            size: originalColor.size, // 恢复原始尺寸
            font: {
              size: $("#dropdown-menu input[name='showLabels']").prop("checked")
                ? 14
                : 0, // 恢复标签显示状态
              face: "Tahoma",
              color: "#000000", // 恢复默认标签颜色
              bold: false,
            },
          });

          // 恢复与该节点相连的边的样式
          var connectedEdges = network.getConnectedEdges(nodeId);
          connectedEdges.forEach(function (edgeId) {
            updateEdges.push({
              id: edgeId,
              color: {
                color: "#848484", // 恢复默认边颜色
                highlight: "#848484",
              },
              width: 2, // 恢复默认边宽度
              shadow: {
                enabled: false, // 移除阴影
              },
            });
          });
        });

        if (updateNodes.length > 0) {
          nodes.update(updateNodes);
        }
        if (updateEdges.length > 0) {
          edges.update(updateEdges);
        }

        // 清空存储的颜色信息
        originalNodeColors = {};

        // 取消选中
        network.unselectAll();
      }

      // 重置搜索视图
      function resetSearchView() {
        // 重置搜索高亮
        resetSearchHighlight();

        // 清空搜索结果
        searchResults = [];
        currentSearchIndex = 0;

        // 清空搜索输入框
        $("#search-input").val("");

        // 恢复到初始视图
        if (network) {
          network.moveTo({
            position: initialViewPosition,
            scale: initialViewScale,
            animation: {
              duration: 600,
              easingFunction: "easeInOutQuad",
            },
          });

          // 更新缩放显示
          setTimeout(function () {
            updateZoomLevel(initialViewScale);
          }, 600);
        }

        layer.msg("已重置搜索视图", { time: 1500 });
      }

      // 添加键盘快捷键支持
      $(document).keydown(function (e) {
        // Ctrl+F 聚焦搜索框
        if (e.ctrlKey && e.keyCode === 70) {
          e.preventDefault();
          $("#search-input").focus().select();
        }

        // ESC 键重置搜索
        if (e.keyCode === 27) {
          resetSearchView();
        }
      });

      // ========== D3.js 思维导图实现 ==========
      var d3MindmapData = null;
      var d3Svg = null;
      var d3Container = null;
      var d3Zoom = null;
      var currentEngine = "d3"; // 默认使用D3引擎

      // 初始化D3思维导图
      function initD3Mindmap(nodes, edges) {
        // 转换数据格式为D3树结构
        var treeData = convertToTreeData(nodes, edges);

        // 设置SVG容器
        var container = document.getElementById("mynetwork");
        var containerRect = container.getBoundingClientRect();

        d3Svg = d3
          .select("#d3-mindmap")
          .attr("width", containerRect.width)
          .attr("height", containerRect.height)
          .style("display", "block");

        // 清除之前的内容
        d3Svg.selectAll("*").remove();

        // 创建主容器组
        d3Container = d3Svg.append("g");

        // 设置缩放行为
        d3Zoom = d3
          .zoom()
          .scaleExtent([0.1, 3])
          .on("zoom", function (event) {
            d3Container.attr("transform", event.transform);
            updateZoomLevel(event.transform.k);
          });

        d3Svg.call(d3Zoom);

        // 绘制思维导图
        drawD3Mindmap(treeData);

        // 初始视图居中
        setTimeout(() => {
          fitD3View();
        }, 500); // 增加延迟，确保DOM完全渲染
      }

      // 转换数据为D3树结构
      function convertToTreeData(nodes, edges) {
        if (!nodes || nodes.length === 0) {
          console.warn("没有节点数据");
          return null;
        }

        // 创建节点映射
        var nodeMap = {};
        nodes.forEach((node) => {
          nodeMap[node.id] = {
            id: node.id,
            data: node,
            children: [],
          };
        });

        // 找到根节点（变压器节点）
        var rootNode = null;
        nodes.forEach((node) => {
          if (node.psrTypeName === "柱上变压器(公用变)") {
            rootNode = nodeMap[node.id];
          }
        });

        // 如果没找到变压器，使用第一个节点作为根
        if (!rootNode && nodes.length > 0) {
          rootNode = nodeMap[nodes[0].id];
        }

        if (!rootNode) {
          console.warn("无法找到根节点");
          return null;
        }

        // 构建树结构
        if (edges && edges.length > 0) {
          edges.forEach((edge) => {
            var parent = nodeMap[edge.from];
            var child = nodeMap[edge.to];
            if (parent && child && parent !== child) {
              parent.children.push(child);
            }
          });
        }

        console.log("转换后的树结构:", rootNode);
        return rootNode;
      }

      // 绘制D3思维导图
      function drawD3Mindmap(treeData) {
        if (!treeData) {
          console.warn("没有树数据可绘制");
          return;
        }

        var containerRect = document
          .getElementById("mynetwork")
          .getBoundingClientRect();
        var width = containerRect.width || 800;
        var height = containerRect.height || 600;

        console.log("容器尺寸:", width, height);

        // 创建树布局
        var tree = d3
          .tree()
          .size([height - 100, width - 200])
          .separation((a, b) => {
            return a.parent === b.parent ? 1.5 : 2;
          });

        // 生成层次数据
        var root = d3.hierarchy(treeData);

        // 计算树布局
        tree(root);

        console.log("树节点数据:", root.descendants());
        console.log("根节点:", root);

        // 调整坐标系（横向布局）
        root.descendants().forEach((d, i) => {
          // 先检查原始坐标
          console.log(`节点 ${i} 原始坐标: x=${d.x}, y=${d.y}`);

          // 如果坐标无效，使用默认值
          if (isNaN(d.x) || isNaN(d.y)) {
            console.warn("发现无效坐标，使用默认值:", d);
            d.x = 100 + i * 150; // 水平排列
            d.y = 50 + (d.depth || 0) * 100; // 垂直分层
          } else {
            // 坐标转换：横向布局
            var temp = d.x;
            d.x = d.y + 100;
            d.y = temp + 50;
          }

          console.log(`节点 ${i} 最终坐标: x=${d.x}, y=${d.y}`);
        });

        var treeNodes = root;

        // 绘制连线（思维导图样式）
        var links = d3Container
          .selectAll(".link")
          .data(treeNodes.links())
          .enter()
          .append("path")
          .attr("class", "link")
          .attr("d", (d) => drawMindmapPath(d))
          .style("fill", "none")
          .style("stroke", "#848484")
          .style("stroke-width", 2)
          .style("stroke-linecap", "round");

        // 绘制节点
        var nodeGroups = d3Container
          .selectAll(".node")
          .data(treeNodes.descendants())
          .enter()
          .append("g")
          .attr("class", "node")
          .attr("transform", (d) => {
            // 确保坐标有效
            var x = isNaN(d.x) ? 100 : d.x;
            var y = isNaN(d.y) ? 50 : d.y;
            console.log(`节点 ${d.data.id} 坐标: (${x}, ${y})`);
            return `translate(${x},${y})`;
          })
          .style("cursor", "pointer");

        // 添加节点图片
        nodeGroups
          .append("image")
          .attr("xlink:href", (d) => d.data.data.image)
          .attr("x", -15)
          .attr("y", -15)
          .attr("width", 30)
          .attr("height", 30)
          .style("border-radius", "50%");

        // 添加节点边框
        nodeGroups
          .append("circle")
          .attr("r", 17)
          .style("fill", "none")
          .style("stroke", "#2B7CE9")
          .style("stroke-width", 2);

        // 添加节点标签
        nodeGroups
          .append("text")
          .attr("dy", 35)
          .attr("text-anchor", "middle")
          .style("font-size", "12px")
          .style("font-family", "Tahoma")
          .style("fill", "#333")
          .text((d) => d.data.data.psrTypeName || d.data.data.label);

        // 添加交互事件
        nodeGroups
          .on("click", function (event, d) {
            handleNodeClick(d.data.data);
          })
          .on("dblclick", function (event, d) {
            handleNodeDoubleClick(d.data.data);
          })
          .on("mouseover", function (event, d) {
            d3.select(this)
              .select("circle")
              .style("stroke", "#FF4500")
              .style("stroke-width", 3);
          })
          .on("mouseout", function (event, d) {
            d3.select(this)
              .select("circle")
              .style("stroke", "#2B7CE9")
              .style("stroke-width", 2);
          });
      }

      // 绘制思维导图路径
      function drawMindmapPath(d) {
        var source = d.source;
        var target = d.target;

        // 确保坐标有效
        var sourceX = isNaN(source.x) ? 100 : source.x;
        var sourceY = isNaN(source.y) ? 50 : source.y;
        var targetX = isNaN(target.x) ? 200 : target.x;
        var targetY = isNaN(target.y) ? 50 : target.y;

        // 计算中间转折点
        var midX = sourceX + (targetX - sourceX) * 0.6;

        return `M${sourceX},${sourceY}
                    L${midX},${sourceY}
                    L${midX},${targetY}
                    L${targetX},${targetY}`;
      }

      // 适应D3视图
      function fitD3View() {
        if (!d3Svg || !d3Container) {
          console.warn("D3 SVG或容器未初始化");
          return;
        }

        try {
          // 等待DOM渲染完成
          setTimeout(() => {
            var bounds = d3Container.node().getBBox();
            console.log("容器边界:", bounds);

            var containerRect = document
              .getElementById("d3-mindmap")
              .getBoundingClientRect();

            var width = containerRect.width || 800;
            var height = containerRect.height || 600;

            console.log("SVG容器尺寸:", width, height);

            // 检查边界是否有效
            if (!bounds || bounds.width <= 0 || bounds.height <= 0) {
              console.warn("无效的边界数据，使用默认视图");
              // 使用简单的居中变换
              var centerX = width / 2;
              var centerY = height / 2;
              d3Svg.call(
                d3Zoom.transform,
                d3.zoomIdentity.translate(centerX - 400, centerY - 200).scale(1)
              );
              updateZoomLevel(1);
              return;
            }

            var scale =
              Math.min(width / bounds.width, height / bounds.height) * 0.8;

            // 确保缩放比例有效
            if (isNaN(scale) || scale <= 0 || !isFinite(scale)) {
              console.warn("无效的缩放比例，使用默认值");
              scale = 1;
            }

            var translateX =
              (width - bounds.width * scale) / 2 - bounds.x * scale;
            var translateY =
              (height - bounds.height * scale) / 2 - bounds.y * scale;

            // 确保平移值有效
            if (isNaN(translateX) || !isFinite(translateX)) {
              console.warn("无效的X平移值，使用默认值");
              translateX = width / 2 - 400;
            }
            if (isNaN(translateY) || !isFinite(translateY)) {
              console.warn("无效的Y平移值，使用默认值");
              translateY = height / 2 - 200;
            }

            console.log("最终变换参数:", { scale, translateX, translateY });

            // 最后一次检查所有参数
            if (
              isNaN(scale) ||
              isNaN(translateX) ||
              isNaN(translateY) ||
              !isFinite(scale) ||
              !isFinite(translateX) ||
              !isFinite(translateY)
            ) {
              console.error("发现无效的变换参数，使用安全默认值");
              scale = 1;
              translateX = 100;
              translateY = 100;
            }

            console.log("安全检查后的变换参数:", {
              scale,
              translateX,
              translateY,
            });

            try {
              d3Svg
                .transition()
                .duration(750)
                .call(
                  d3Zoom.transform,
                  d3.zoomIdentity.translate(translateX, translateY).scale(scale)
                );
            } catch (transformError) {
              console.error("变换执行失败:", transformError);
              // 使用最简单的变换
              d3Svg.call(
                d3Zoom.transform,
                d3.zoomIdentity.translate(100, 100).scale(1)
              );
            }

            updateZoomLevel(scale);
          }, 100);
        } catch (error) {
          console.error("适应视图时出错:", error);
          // 使用安全的默认变换
          try {
            d3Svg.call(
              d3Zoom.transform,
              d3.zoomIdentity.translate(100, 100).scale(1)
            );
            updateZoomLevel(1);
          } catch (e) {
            console.error("默认变换也失败:", e);
          }
        }
      }
    </script>
  </body>
</html>
